export type FiltersProps = {
  setFilters: (filters: FilterOptions) => void;
  filters?: FilterOptions;
};

export enum DeliverableType {
  KPI = 'KPI',
  PROJECT_YES_NO = 'PROJECT_YES_NO',
  SCOPED_PROJECT_YES_NO = 'SCOPED_PROJECT_YES_NO',
  PROJECT = 'PROJECT',
  MASTER_PROJECT = 'MASTER_PROJECT',
}

export enum SortType {
  ASC = 'ASC',
  DESC = 'DESC',
}

export enum OrderBy {
  ASC = 'asc',
  DESC = 'desc',
}

export enum SortBy {
  NAME = 'name',
  USAGE = 'usage',
}

export interface SortOptions {
  orderBy: OrderBy;
  sortBy: SortBy;
}

export interface FilterOptions {
  deliverableTypes?: string[];
  isActive?: boolean;
  businessFunctions: string[];
  stagedBusinessFunctions?: string[];
  fuzzy_search?: string;
  sortType?: SortType | null | undefined;
  orderBy?: OrderBy | null | undefined;
  sortBy?: SortBy | null | undefined;
  forceSearch?: boolean;
}
