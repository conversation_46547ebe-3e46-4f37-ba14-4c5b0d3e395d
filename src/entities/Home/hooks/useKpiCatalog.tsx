import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';

import businessFunctionsService from '~/shared/services/businessFunctions';
import deliverablesService from '~/shared/services/deliverables';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { DeliverableItemsResponse } from '~/shared/types/DeliverablesService';
import { GetDeliverableFilters } from '~/shared/types/GetDeliverableFilters';
import { CatalogListingTypeEnum } from '~/shared/utils/enums';

import { DeliverableType, FilterOptions } from '../components/Filters/types';

const PAGE_SIZE = 10;

const mapFiltersToApiParams = (
  filters: FilterOptions,
  pageNumber: number = 1,
  pageSize: number = PAGE_SIZE,
): GetDeliverableFilters => {
  const apiFilters: GetDeliverableFilters = {
    pageNumber,
    pageSize,
    ...(filters.fuzzy_search && { search: filters.fuzzy_search }),
    ...(filters.businessFunctions?.length
      ? { businessFunctions: filters.businessFunctions }
      : {}),
    ...(filters.isActive !== undefined ? { isActive: filters.isActive } : {}),
    ...(filters.sortType ? { sortType: filters.sortType } : {}),
    ...(filters.orderBy ? { orderBy: filters.orderBy } : {}),
    ...(filters.sortBy ? { sortBy: filters.sortBy } : {}),
  };

  const selectedTypes = filters.deliverableTypes
    ? Array.isArray(filters.deliverableTypes)
      ? filters.deliverableTypes
      : [filters.deliverableTypes]
    : [];

  if (selectedTypes.length) {
    const typeMap: Record<string, DeliverableType[]> = {
      KPI: [DeliverableType.KPI],
      PROJECT: [
        DeliverableType.PROJECT,
        DeliverableType.MASTER_PROJECT,
        DeliverableType.PROJECT_YES_NO,
      ],
    };

    const deliverableTypes = selectedTypes.flatMap(t => typeMap[t] ?? []);
    if (deliverableTypes.length) {
      apiFilters.deliverableTypes = deliverableTypes;
    }
  } else {
    apiFilters.deliverableTypes = [
      DeliverableType.KPI,
      DeliverableType.PROJECT_YES_NO,
      DeliverableType.PROJECT,
      DeliverableType.MASTER_PROJECT,
    ];

    if (filters.isActive === undefined) {
      apiFilters.isActive = true;
    }
  }

  return apiFilters;
};

const hasAnyActiveFilters = (filters: FilterOptions, pageNumber: number) =>
  Boolean(
    filters.fuzzy_search ||
      filters.businessFunctions?.length ||
      filters.isActive !== undefined ||
      filters.deliverableTypes?.length ||
      filters.sortType ||
      filters.forceSearch ||
      pageNumber !== 1,
  );

export function useKpiCatalog(
  pageNumber: number = 1,
  pageSize: number = PAGE_SIZE,
  initialKpis?: DeliverableItemsResponse,
) {
  const router = useRouter();

  const [displayMode, setDisplayMode] = useState<CatalogListingTypeEnum>(
    CatalogListingTypeEnum.GRID,
  );

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterOptions>({
    deliverableTypes: [],
    isActive: undefined,
    businessFunctions: [],
    stagedBusinessFunctions: [],
    fuzzy_search: '',
    sortType: null,
  });

  const effectiveFiltersForQuery = useMemo(() => {
    const { stagedBusinessFunctions, ...rest } = filters;
    return rest;
  }, [filters]);

  // Track whether filters were ever used so we don't reuse initial SSR data after first interaction
  const [hasFiltersBeenUsed, setHasFiltersBeenUsed] = useState(false);
  const hasActiveFilters = useMemo(
    () => hasAnyActiveFilters(filters, pageNumber),
    [filters, pageNumber],
  );

  useEffect(() => {
    if (hasActiveFilters && !hasFiltersBeenUsed) {
      setHasFiltersBeenUsed(true);
    }
  }, [hasActiveFilters, hasFiltersBeenUsed]);

  const [kpiData, setKpiData] = useState<DeliverableItem | null>(() => {
    if (typeof window === 'undefined') {
      return null;
    }
    try {
      const saved = window.sessionStorage.getItem('kpi-data');
      return saved ? JSON.parse(saved) : null;
    } catch {
      return null;
    }
  });

  const { data: businessFunctions = [] } = useQuery({
    queryKey: ['business-functions'],
    queryFn: () => businessFunctionsService.getBusinessFunctions(),
    select: list => list.map(bf => bf.label),
  });

  const shouldUseInitialData = Boolean(
    initialKpis?.data?.length &&
      !hasFiltersBeenUsed &&
      pageNumber === 1 &&
      pageSize === PAGE_SIZE &&
      !filters.fuzzy_search &&
      !filters.businessFunctions?.length &&
      filters.isActive === undefined &&
      !filters.deliverableTypes?.length &&
      !filters.sortType &&
      !filters.orderBy &&
      !filters.sortBy,
  );

  const {
    data: infiniteKpis,
    isLoading: infiniteKpisLoading,
    isError: infiniteKpisError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    queryKey: ['getDeliverablesInfinite', effectiveFiltersForQuery, pageSize],
    queryFn: ({ pageParam = 1 }) => {
      const apiFilters = mapFiltersToApiParams(
        effectiveFiltersForQuery,
        pageParam as number,
        pageSize,
      );
      return deliverablesService.getDeliverables(apiFilters);
    },
    enabled: !shouldUseInitialData,
    initialData: shouldUseInitialData
      ? { pages: [initialKpis!], pageParams: [1] }
      : undefined,
    getNextPageParam: (lastPage: DeliverableItemsResponse, allPages) => {
      const currentPage = allPages.length;
      const totalPages = Math.ceil(lastPage.totalRecords / pageSize);
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    refetchOnWindowFocus: false,
    staleTime: 0,
  });

  const finalKpis = infiniteKpis?.pages?.length
    ? displayMode === CatalogListingTypeEnum.LIST
      ? {
          // LIST: All pages combined for infinite scroll
          data: infiniteKpis.pages.flatMap(page => page.data),
          totalRecords: infiniteKpis.pages[0]?.totalRecords || 0,
          totalPages: Math.ceil((infiniteKpis.pages[0]?.totalRecords || 0) / pageSize),
        }
      : {
          // GRID: Current page only for pagination
          data: infiniteKpis.pages[pageNumber - 1]?.data || [],
          totalRecords: infiniteKpis.pages[0]?.totalRecords || 0,
          totalPages: Math.ceil((infiniteKpis.pages[0]?.totalRecords || 0) / pageSize),
        }
    : shouldUseInitialData ? initialKpis : null;

  const shouldShowLoading = shouldUseInitialData ? false : infiniteKpisLoading;

  const { data: kpiDetails, isLoading: kpiLoading } = useQuery({
    queryKey: ['getDeliverable', selectedItemId],
    queryFn: () =>
      deliverablesService.getDeliverableById(selectedItemId as string),
    enabled: !!selectedItemId,
  });

  useEffect(() => {
    if (kpiDetails) {
      setKpiData(kpiDetails);
      if (typeof window !== 'undefined') {
        try {
          window.sessionStorage.setItem('kpi-data', JSON.stringify(kpiDetails));
        } catch {}
      }
    }
  }, [kpiDetails]);

  const toggleDisplayMode = useCallback(() => {
    setDisplayMode(prevMode =>
      prevMode === CatalogListingTypeEnum.GRID
        ? CatalogListingTypeEnum.LIST
        : CatalogListingTypeEnum.GRID,
    );
  }, []);

  const handleNavigateToItemDetails = useCallback(
    (id: string) => {
      if (!id || id === 'null') {
        console.error('Invalid catalog item ID:', id);
        return;
      }
      void router.push(`/deliverable-details/${id}`);
    },
    [router],
  );

  const handleOpenDrawer = useCallback((id: string) => {
    setSelectedItemId(id);
    setIsDrawerOpen(true);
  }, []);

  const handleCloseDrawer = useCallback(() => {
    setIsDrawerOpen(false);
    setSelectedItemId(null);
  }, []);

  return {
    displayMode,
    toggleDisplayMode,
    kpis: finalKpis,
    kpisLoading: shouldShowLoading,
    kpiData,
    kpiLoading,
    handleNavigateToItemDetails,
    isDrawerOpen,
    selectedItemId,
    handleOpenDrawer,
    handleCloseDrawer,
    filters,
    setFilters,
    businessFunctions,
    kpisError: infiniteKpisError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  };
}
